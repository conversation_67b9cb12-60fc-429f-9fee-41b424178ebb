{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}
{extends file=$layout}

{block name='head' append}
  <meta property="og:type" content="product">
  {if $product.cover}
  <meta property="og:image" content="{$product.cover.large.url}">
  {/if}

  {if $product.show_price}
  <meta property="product:pretax_price:amount" content="{$product.price_tax_exc}">
  <meta property="product:pretax_price:currency" content="{$currency.iso_code}">
  <meta property="product:price:amount" content="{$product.price_amount}">
  <meta property="product:price:currency" content="{$currency.iso_code}">
  {/if}
  {if isset($product.weight) && ($product.weight != 0)}
  <meta property="product:weight:value" content="{$product.weight}">
  <meta property="product:weight:units" content="{$product.weight_unit}">
  {/if}
{/block}

{block name='head_microdata_special'}
  {include file='_partials/microdata/product-jsonld.tpl'}
{/block}

{block name='content'}
<section id="main">
    <meta content="{$product.url}">
    <div class="product-container js-product-container row">
        <div class="pp-left-column col-xs-12 col-sm-6 col-md-6">
          {block name='page_content_container'}
            <section class="page-content" id="content">
              <div class="product-leftside">
                {block name='page_content'}
                  {block name='product_cover_thumbnails'}
                    {include file='catalog/_partials/product-cover-thumbnails.tpl'}
                  {/block}
                {/block}
              </div>
            </section>
          {/block}
        </div>
    
        <div class="pp-right-column col-xs-12  col-sm-6 col-md-6">
          
          <!-- Codezeel added -->
          {block name='product_reviews'}
              {hook h='displayProductListReviews' product=$product}
          {/block}

          {block name='page_header_container'}
            {block name='page_header'}
              <h2 class="h1 productpage_title">{block name='page_title'}{$product.name}{/block}</h2>
            {/block}
          {/block}



      {if isset($product.reference_to_display) && $product.reference_to_display neq ''}
  <div class="product-reference-header">
    <label class="label">{l s='Reference' d='Shop.Theme.Catalog'}: </label>
    <span class="reference-value js-product-reference" data-product-reference="{$product.reference_to_display}">{$product.reference_to_display}</span>
  </div>
{/if}

          <div class="product-information">
              {block name='product_description_short'}
                <div id="product-description-short-{$product.id}" itemprop="description">{$product.description_short nofilter}</div>
              {/block}

              <div class="product-actions js-product-actions">
                {block name='product_buy'}
                  <form action="{$urls.pages.cart}" method="post" id="add-to-cart-or-refresh">
                    <input type="hidden" name="token" value="{$static_token}">
                    <input type="hidden" name="id_product" value="{$product.id}" id="product_page_product_id">
                    <input type="hidden" name="id_customization" value="{$product.id_customization}" id="product_customization_id" class="js-product-customization-id">

{if $product.is_customizable && count($product.customizations.fields)}
                {block name='product_customization'}
                  {include file="catalog/_partials/product-customization.tpl" customizations=$product.customizations}
                {/block}
              {/if}

                    {block name='product_variants'}
                      {include file='catalog/_partials/product-variants.tpl'}
                    {/block}

                    {block name='product_pack'}
                      {if $packItems}
                        <section class="product-pack">
                          <h5>{l s='This pack contains' d='Shop.Theme.Catalog'}</h3>
                          {foreach from=$packItems item="product_pack"}
                            {block name='product_miniature'}
                              {include file='catalog/_partials/miniatures/pack-product.tpl' product=$product_pack showPackProductsPrice=$product.show_price}
                            {/block}
                          {/foreach}
                        </section>
                      {/if}
                    {/block}

                    {block name='product_discounts'}
                      {include file='catalog/_partials/product-discounts.tpl'}
                    {/block}

                    {block name='product_prices'}
                      {include file='catalog/_partials/product-prices.tpl'}
                    {/block}
            
                    {block name='product_add_to_cart'}
                      {include file='catalog/_partials/product-add-to-cart.tpl'}
                    {/block}

                  {*{$productTags = Tag::getProductTags($product.id)}
                    {if !empty($productTags)}
                      <div class="producttags">
                        <label class="label">{l s='Tags' d='Shop.Theme.Catalog'}: </label>
                        {foreach from=$productTags key=k item=v}
                          {foreach from=$v item=value}
                          <a href="{$link->getPageLink('search', true, NULL, "tag={$value|urlencode}")}">{$value|escape:html:'UTF-8'}</a> 
                          {/foreach}
                        {/foreach}
                      </div>
                    {/if}*}

                    {block name='product_additional_info'}
                      {include file='catalog/_partials/product-additional-info.tpl'}
                    {/block}

                    {* Input to refresh product HTML removed, block kept for compatibility with themes *}
                    {block name='product_refresh'}{/block}
            
                  </form>
                {/block}
              </div>
          </div>

          {block name='hook_display_reassurance'}
              {hook h='displayReassurance'}
          {/block}
        </div> 
    </div>
       
    <section class="product-tabcontent">	
      {block name='product_tabs'}
        <div class="tabs">
            <ul class="nav nav-tabs" role="tablist">
              {if $product.description}
                <li class="nav-item">
                    <a
                      class="nav-link{if $product.description} active js-product-nav-active{/if}"
                      data-toggle="tab"
                      href="#description"
                      role="tab"
                      aria-controls="description"
                      {if $product.description} aria-selected="true"{/if}>{l s='Description' d='Shop.Theme.Catalog'}</a>
                </li>
              {/if}
              <li class="nav-item">
                <a
                  class="nav-link{if !$product.description} active js-product-nav-active{/if}"
                  data-toggle="tab"
                  href="#product-details"
                  role="tab"
                  aria-controls="product-details"
                  {if !$product.description} aria-selected="true"{/if}>{l s='Product Details' d='Shop.Theme.Catalog'}</a>
              </li>
              {if $product.attachments}
                <li class="nav-item">
                  <a
                    class="nav-link"
                    data-toggle="tab"
                    href="#attachments"
                    role="tab"
                    aria-controls="attachments">{l s='Attachments' d='Shop.Theme.Catalog'}</a>
                </li>
              {/if}
              {foreach from=$product.extraContent item=extra key=extraKey}
                <li class="nav-item">
                  <a
                    class="nav-link"
                    data-toggle="tab"
                    href="#extra-{$extraKey}"
                    role="tab"
                    aria-controls="extra-{$extraKey}">{$extra.title}</a>
                </li>
              {/foreach}
              {hook h='customTab'}
            </ul>

            <div class="tab-content" id="tab-content">
              <div class="tab-pane fade in{if $product.description} active js-product-tab-active{/if}" id="description" role="tabpanel">
                {block name='product_description'}
                  <div class="product-description">{$product.description nofilter}</div>
                {/block}
              </div>

              {block name='product_details'}
                {include file='catalog/_partials/product-details.tpl'}
              {/block}

              {block name='product_attachments'}
                {if $product.attachments}
                  <div class="tab-pane fade in" id="attachments" role="tabpanel">
                    <section class="product-attachments">
                      <h3 class="h5 text-uppercase">{l s='Download' d='Shop.Theme.Actions'}</h3>
                      {foreach from=$product.attachments item=attachment}
                        <div class="attachment">
                          <h4><a href="{url entity='attachment' params=['id_attachment' => $attachment.id_attachment]}">{$attachment.name}</a></h4>
                          <p>{$attachment.description}</p
                          ><a href="{url entity='attachment' params=['id_attachment' => $attachment.id_attachment]}">
                            {l s='Download' d='Shop.Theme.Actions'} ({$attachment.file_size_formatted})
                          </a>
                        </div>
                      {/foreach}
                    </section>
                  </div>
                {/if}
              {/block}
              {foreach from=$product.extraContent item=extra key=extraKey}
                <div class="tab-pane fade in {$extra.attr.class}" id="extra-{$extraKey}" role="tabpanel" {foreach $extra.attr as $key => $val} {$key}="{$val}"{/foreach}>
                  {$extra.content nofilter}
              </div>
              {/foreach}
              {hook h='customTabContent'}
            </div>
        </div>
      {/block}
    </section>
	
    {block name='product_accessories'}
        {if $accessories}
          {assign var='sliderFor' value=7} <!-- Define Number of product for SLIDER -->
          {assign var='productCount' value=count($accessories)}
      
          <section class="product-accessories products-section clearfix">
            <h2 class="h1 products-section-title text-uppercase">
              {l s='You might also like' d='Shop.Theme.Catalog'}
            </h2>

            <div class="product-wrapper">
              <div class="products"> 
                {if $productCount >= $sliderFor}
                  <div id="accessories-carousel" class="cz-carousel product_list">
                {else}
                  <div id="accessories-grid" class="accessories_grid product_list grid row gridcount">
                {/if}

                {foreach from=$accessories item="product_accessory"}
                  {block name='product_miniature'}
                    <article class="product_item {if $productCount >= $sliderFor} slider_item{else} grid_item col-xs-12 col-sm-6 col-md-4 col-lg-3{/if}">
                      {include file='catalog/_partials/miniatures/product.tpl' product=$product_accessory}
                    </article>
                  {/block}
                {/foreach}
                </div>
            
                {if $productCount >= $sliderFor}
                  <div class="customNavigation">
                    <a class="btn prev accessories_prev">&nbsp;</a>
                    <a class="btn next accessories_next">&nbsp;</a>
                  </div>
                {/if}

              </div>
            </div>
          </section>
        {/if}
    {/block}

    {block name='product_footer'}
      {hook h='displayFooterProduct' product=$product category=$category}
    {/block}

    {block name='product_images_modal'}
      {include file='catalog/_partials/product-images-modal.tpl'}
    {/block}

    {block name='page_footer_container'}
      <footer class="page-footer">
        {block name='page_footer'}
          <!-- Footer content -->
        {/block}
      </footer>
    {/block}
  </div>
</section>

{literal}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Product reference updater initialized');

    // Funkce pro získání reference varianty přes AJAX
    async function getVariantReference(productId, attributeId) {
        console.log('=== Calling getVariantReference ===');
        console.log('Product ID:', productId, 'Attribute ID:', attributeId);

        try {
            const url = `${prestashop.urls.base_url}index.php?controller=product&ajax=1&action=refresh&id_product=${productId}&id_product_attribute=${attributeId}`;
            console.log('AJAX URL:', url);

            const response = await fetch(url);
            console.log('Response status:', response.status);

            if (!response.ok) {
                console.error('Error loading variant data:', response.status);
                const errorText = await response.text();
                console.error('Error response:', errorText);
                return null;
            }

            const data = await response.json();
            console.log('AJAX response data (full):', data);

            // PrestaShop vrací reference v různých místech
            if (data.product_details && data.product_details.reference) {
                console.log('Found reference in product_details:', data.product_details.reference);
                return data.product_details.reference;
            }

            // Zkusíme najít reference v jiných místech odpovědi
            if (data.product && data.product.reference) {
                console.log('Found reference in product:', data.product.reference);
                return data.product.reference;
            }

            if (data.reference) {
                console.log('Found reference directly:', data.reference);
                return data.reference;
            }

            // Zkusíme najít reference v product_details.embedded_attributes
            if (data.product_details && data.product_details.embedded_attributes && data.product_details.embedded_attributes.reference) {
                console.log('Found reference in embedded_attributes:', data.product_details.embedded_attributes.reference);
                return data.product_details.embedded_attributes.reference;
            }

            console.log('No reference found in AJAX response');
            return null;
        } catch (error) {
            console.error('Error fetching variant reference:', error);
            return null;
        }
    }

    // Funkce pro aktualizaci reference z URL/AJAX
    async function updateReferenceFromUrl() {
        const currentUrl = window.location.href;
        const hashMatch = currentUrl.match(/#\/(\d+)-/);

        if (hashMatch) {
            const attributeId = hashMatch[1];
            const productId = document.querySelector('#product_page_product_id')?.value;

            console.log('URL attribute ID:', attributeId, 'Product ID:', productId);

            if (productId && attributeId) {
                // Získáme skutečnou referenci přes AJAX
                const reference = await getVariantReference(productId, attributeId);

                if (reference) {
                    const referenceElement = document.querySelector('.js-product-reference');
                    if (referenceElement) {
                        referenceElement.textContent = reference;
                        referenceElement.setAttribute('data-product-reference', reference);
                        console.log('Updated reference from AJAX to:', reference);
                    }
                } else {
                    console.log('No reference found in AJAX response');
                }
            }
        }
    }

    // Posloucháme na PrestaShop události
    if (typeof prestashop !== 'undefined') {
        // Událost při aktualizaci produktu
        prestashop.on('updatedProduct', function(event) {
            console.log('PrestaShop updatedProduct event:', event);

            const referenceElement = document.querySelector('.js-product-reference');

            if (referenceElement && event.product_details && event.product_details.reference) {
                referenceElement.textContent = event.product_details.reference;
                referenceElement.setAttribute('data-product-reference', event.product_details.reference);
                console.log('Updated reference from PrestaShop event to:', event.product_details.reference);
            }
        });

        // Událost při změně URL (history change)
        prestashop.on('updateProduct', function(event) {
            console.log('PrestaShop updateProduct event:', event);
            setTimeout(updateReferenceFromUrl, 100);
        });
    }

    // Posloucháme na změny v URL (pro případ, že PrestaShop události nefungují)
    let lastUrl = window.location.href;
    const urlObserver = new MutationObserver(function() {
        if (lastUrl !== window.location.href) {
            lastUrl = window.location.href;
            console.log('URL changed to:', lastUrl);
            setTimeout(updateReferenceFromUrl, 200);
        }
    });

    urlObserver.observe(document, { subtree: true, childList: true });

    // Posloucháme na změny ve formuláři variant
    const variantForm = document.querySelector('#add-to-cart-or-refresh');
    if (variantForm) {
        variantForm.addEventListener('change', function(event) {
            if (event.target.matches('select[data-product-attribute], input[data-product-attribute]')) {
                console.log('Variant form changed:', event.target);
                setTimeout(updateReferenceFromUrl, 300);
            }
        });
    }

    // Posloucháme na popstate události (zpět/vpřed v prohlížeči)
    window.addEventListener('popstate', function() {
        console.log('Popstate event');
        setTimeout(updateReferenceFromUrl, 100);
    });

    // Prvotní kontrola URL při načtení stránky
    updateReferenceFromUrl();
});
</script>
{/literal}

{/block}
