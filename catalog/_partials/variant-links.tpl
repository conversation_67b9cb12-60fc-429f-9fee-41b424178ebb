<div class="variant-links">
  {foreach from=$variants item=variant}
    <a href="{$variant.url}"
       class="{$variant.type} {if $variant.texture}texture{/if}"
       title="{$variant.name}"
       data-variant-name="{$variant.name|escape:'html':'UTF-8'}"
       data-variant-url="{$variant.url}"
       {* Pokusíme se najít obrázek varianty různými způsoby *}
       {if isset($variant.cover) && $variant.cover}
         data-variant-image="{$variant.cover.bySize.home_default.url}"
         data-variant-image-large="{$variant.cover.large.url}"
       {elseif isset($variant.image) && $variant.image}
         data-variant-image="{$variant.image.bySize.home_default.url}"
         data-variant-image-large="{$variant.image.large.url}"
       {elseif isset($variant.images) && $variant.images|count > 0}
         data-variant-image="{$variant.images[0].bySize.home_default.url}"
         data-variant-image-large="{$variant.images[0].large.url}"
       {/if}
       {if isset($variant.id_product_attribute) && $variant.id_product_attribute}
         data-variant-attribute-id="{$variant.id_product_attribute}"
       {/if}
       {if $variant.html_color_code}
         data-color-code="{$variant.html_color_code}"
         style="background-color: {$variant.html_color_code}"
       {/if}
       {if $variant.texture} 
         style="background-image: url({$variant.texture})" 
         data-texture="{$variant.texture}"
       {/if}
    ><span class="sr-only">{$variant.name}</span></a>
  {/foreach}
  <span class="js-count count"></span>
</div>