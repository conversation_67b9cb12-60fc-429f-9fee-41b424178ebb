{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future.
 * If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 *}
{block name='product_miniature_item'}
<div class="product-miniature js-product-miniature" data-id-product="{$product.id_product}" data-id-product-attribute="{$product.id_product_attribute}">
  	<div class="thumbnail-container">
	    {block name='product_thumbnail'}
			{* Jednoduché přiřazení výchozích hodnot. O zbytek se postará JS. *}
			{assign var="display_cover" value=$product.cover}
			{assign var="display_url" value=$product.url}

			{if $display_cover}
				<a href="{$display_url}" class="thumbnail product-thumbnail">
					<img
						class="lazyload product-image" 
						src="{$urls.img_url}codezeel/lazy-loader.svg"
						data-src="{$display_cover.bySize.home_default.url}"
						alt="{if !empty($display_cover.legend)}{$display_cover.legend}{else}{$product.name|truncate:30:'...'}{/if}"
						data-full-size-image-url = "{$display_cover.large.url}"
						width="{$display_cover.bySize.home_default.width}"
                		height="{$display_cover.bySize.home_default.height}"
					>
					{hook h="displayCzHoverImage" id_product=$product.id_product home='home_default' large='large_default'}
				</a>
			{else}
				<a href="{$display_url}" class="thumbnail product-thumbnail">
					<img
						class="lazyload" 
						src="{$urls.img_url}codezeel/lazy-loader.svg"
						data-src="{$urls.no_picture_image.bySize.home_default.url}"
						loading="lazy"
						width="{$urls.no_picture_image.bySize.home_default.width}"
                		height="{$urls.no_picture_image.bySize.home_default.height}"
					/>
				</a>
			{/if}
	    {/block}

	    <div class="highlighted-informations{if !$product.main_variants} no-variants{/if}">
			{block name='product_variants'}
				{if $product.main_variants}
					{include file='catalog/_partials/variant-links.tpl' variants=$product.main_variants}
				{/if}
			{/block}
		</div>
  
		{include file='catalog/_partials/product-flags.tpl'}

	    <div class="outer-functional">
			<div class="functional-buttons">
	    		{hook h='displayStWishlistButton' product=$product}
				{hook h='displayStCompareButton' product=$product}

				{block name='quick_view'}
					<div class="quickview">
						<a href="#" class="quick-view js-quick-view" data-link-action="quickview">
							<i class="material-icons search">&#xE417;</i> {l s='Quick view' d='Shop.Theme.Actions'}
						</a>
					</div>
				{/block}
			</div>
		</div>
	</div>

    <div class="product-description">
		{block name='brand_name'}
			<div class="brand-title" itemprop="name">
			<a href="{$link->getmanufacturerLink($product['id_manufacturer'])}">{Manufacturer::getnamebyid($product.id_manufacturer)}</a>
			</div>
		{/block}
		
		{block name='product_name'}
          	<h3 class="h3 product-title"><a href="{$display_url}" content="{$display_url}">{$product.name|truncate:80:'...'}</a></h3>
	    {/block}

		{block name='product_reviews'}
			{hook h='displayProductListReviews' product=$product}
		{/block}

        {block name='product_price_and_shipping'}
			{if $product.show_price}
				<div class="product-price-and-shipping">
					{if $product.has_discount}
						{hook h='displayProductPriceBlock' product=$product type="old_price"}

						<span class="regular-price" aria-label="{l s='Regular price' d='Shop.Theme.Catalog'}">{$product.regular_price}</span>
							{if $product.discount_type === 'percentage'}
								<span class="discount-percentage discount-product">{$product.discount_percentage} <span>{l s='Off' d='Shop.Theme.Global'}</span></span>
							{elseif $product.discount_type === 'amount'}
								<span class="discount-amount discount-product">{$product.discount_amount_to_display}<span> {l s='Off' d='Shop.Theme.Global'}</span></span>
							{/if}
					{/if}

					{hook h='displayProductPriceBlock' product=$product type="before_price"}

					<span class="price" aria-label="{l s='Price' d='Shop.Theme.Catalog'}">
						{capture name='custom_price'}{hook h='displayProductPriceBlock' product=$product type='custom_price' hook_origin='products_list'}{/capture}
						{if '' !== $smarty.capture.custom_price}
						{$smarty.capture.custom_price nofilter}
						{else}
						{$product.price}
						{/if}
					</span>

					{hook h='displayProductPriceBlock' product=$product type='unit_price'}

					{hook h='displayProductPriceBlock' product=$product type='weight'}
				</div>
			{/if}
        {/block}	 

		{block name='product_buy'}
			{if !$configuration.is_catalog}
				<div class="proaction-button">
					{if !$product.main_variants}
						<form action="{$urls.pages.cart}" method="post" class="add-to-cart-or-refresh">
							<input type="hidden" name="token" value="{$static_token}">
							<input type="hidden" name="id_product" value="{$product.id}" class="product_page_product_id">
							<input type="hidden" name="id_customization" value="0" id="product_customization_id" class="js-product-customization-id">
							<button class="btn btn-primary add-to-cart" data-button-action="add-to-cart" type="submit" {if !$product.add_to_cart_url}disabled{/if}>
								{l s='Add to cart' d='Shop.Theme.Actions'}
							</button>
						</form>
					{else}
						<a href="{$display_url}" class="btn btn-primary add-to-cart">
							{l s='Options' d='Shop.Theme.Global'}
						</a>
					{/if}
				</div>
			{/if}
		{/block} 
	</div>
</div>

{literal}
<script>
document.addEventListener('DOMContentLoaded', function() {
    function initProductVariantLogic() {
        // Vždy načteme aktuální miniatury, které ještě nebyly inicializovány
        const productMiniatures = document.querySelectorAll('.js-product-miniature:not(.variant-logic-initialized)');
        if (productMiniatures.length === 0) return;

        function normalizeText(text) {
            if (!text) return '';
            return text.toLowerCase()
                .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
                .replace(/\s+/g, '-');
        }

        function getActiveColorFilters() {
            const filters = new Set();
            const urlParams = new URLSearchParams(window.location.search);
            const qParam = urlParams.get('q');
            if (qParam) {
                const qParts = qParam.split('/');
                qParts.forEach(part => {
                    // Normalizujeme část pro porovnání
                    const normalizedPart = normalizeText(part);
                    // Hledáme "barva-" na začátku (po normalizaci)
                    if (normalizedPart.startsWith('barva-')) {
                        // Extrahujeme název barvy a normalizujeme ho
                        const colorName = normalizedPart.substring(6);
                        filters.add(colorName);
                    }
                });
            }
            return Array.from(filters);
        }

        async function loadVariantImage(productId, attributeId) {
            const moduleDirectoryName = 'filtrace_barev';
            const endpointUrl = `${prestashop.urls.base_url}modules/${moduleDirectoryName}/ajax/get_variant_image_basic.php?id_product=${productId}&id_product_attribute=${attributeId}`;
            
            try {
                const response = await fetch(endpointUrl);
                if (!response.ok) {
                    console.error(`Error loading variant image: Server responded with status ${response.status}`);
                    const errorText = await response.text();
                    console.error('Server response:', errorText);
                    return { success: false, error: `Server returned status ${response.status}` };
                }
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('Error loading variant image via AJAX:', error);
                return { success: false, error: error.message };
            }
        }

        function updateProductDisplay(productElement, variantData) {
            const { url, image, imageLarge } = variantData;

            const productLink = productElement.querySelector('.thumbnail.product-thumbnail');
            const titleLink = productElement.querySelector('.h3.product-title a');
            const productImage = productElement.querySelector('img.product-image');

            if (url) {
                if (productLink) productLink.href = url;
                if (titleLink) titleLink.href = url;
            }
            if (image && productImage) {
                productImage.classList.add('loading');
                // const oldSrc = productImage.src; // Není potřeba, pokud se jen mění src
                productImage.src = image;
                productImage.dataset.src = image;
                if (imageLarge) {
                    productImage.dataset.fullSizeImageUrl = imageLarge;
                }
                // Plynulý přechod po načtení nového obrázku
                const tempImg = new Image();
                tempImg.onload = () => productImage.classList.remove('loading');
                tempImg.onerror = () => productImage.classList.remove('loading'); // i v případě chyby
                tempImg.src = image;
            }
        }

        async function updateProductImages() {
            const activeFilters = getActiveColorFilters();

            // Debug informace
            console.log('=== Filtrace barev - Debug ===');
            console.log('URL:', window.location.href);
            console.log('URL parametr q:', new URLSearchParams(window.location.search).get('q'));
            console.log('Detekované aktivní filtry:', activeFilters);
            console.log('Počet miniatur produktů:', document.querySelectorAll('.js-product-miniature:not(.variant-logic-initialized)').length);

            // Pokud nejsou aktivní filtry, nebo pokud je to stránka detailu produktu, kde se chová jinak, nebudeme nic dělat.
            // Tento skript je primárně pro stránky s výpisem produktů a filtrací.
            if (activeFilters.length === 0) {
                console.log('Žádné aktivní barevné filtry - ukončuji');
                return;
            }

            for (const productElement of productMiniatures) {
                const productId = productElement.dataset.idProduct;
                const currentAttributeId = productElement.dataset.idProductAttribute; // Získáme aktuální ID atributu z miniatury

                console.log(`Zpracovávám produkt ${productId}, varianta ${currentAttributeId}`);

                if (!productId) continue;

                let targetAttributeId = null;
                let targetVariantUrl = null;
                let targetVariantImage = null;
                let targetVariantImageLarge = null;

                // Prioritně se pokusíme najít variantu, která odpovídá aktivnímu filtru
                const variants = productElement.querySelectorAll('.variant-links a[data-variant-name]');
                console.log(`Produkt ${productId} má ${variants.length} variant`);

                for (const variant of variants) {
                    const variantName = variant.dataset.variantName;
                    const normalizedVariantName = normalizeText(variantName);

                    console.log(`Varianta: "${variantName}" -> normalizováno: "${normalizedVariantName}"`);
                    console.log(`Porovnávám s aktivními filtry:`, activeFilters);

                    if (activeFilters.includes(normalizedVariantName)) {
                        targetAttributeId = variant.dataset.variantAttributeId;
                        targetVariantUrl = variant.dataset.variantUrl;
                        targetVariantImage = variant.dataset.variantImage;
                        targetVariantImageLarge = variant.dataset.variantImageLarge;
                        console.log(`✅ Nalezena shoda! Varianta: ${variantName}, ID: ${targetAttributeId}`);
                        break;
                    }
                }

                if (!targetAttributeId) {
                    console.log(`❌ Žádná varianta neodpovídá aktivním filtrům pro produkt ${productId}`);
                }

                // Pokud jsme nenašli shodu s filtrem, ale miniatura má nastavené ID atributu (což by měla mít po filtraci)
                // a toto ID se liší od ID první varianty, zkusíme použít toto ID.
                // Toto je důležité pro případy, kdy PrestaShop již aktualizoval data-id-product-attribute na miniatuře.
                if (!targetAttributeId && currentAttributeId) {
                    // Zde bychom museli najít odpovídající variantu v DOMu nebo volat AJAX pro získání URL a obrázku
                    // Pro zjednodušení a spolehlivost budeme předpokládat, že pokud je currentAttributeId nastaveno,
                    // pak je to varianta, kterou chceme zobrazit.
                    targetAttributeId = currentAttributeId;
                    // Pro získání URL a obrázku budeme muset volat AJAX, pokud nejsou v DOMu.
                    // Zde je to zjednodušeno, protože AJAX volání je již v loadVariantImage.
                }


                if (targetAttributeId) {
                    let imageUrl = targetVariantImage;
                    let imageUrlLarge = targetVariantImageLarge;
                    let variantUrl = targetVariantUrl;

                    // Pokud nemáme URL nebo obrázek z DOMu, zavoláme AJAX
                    if (!imageUrl || !variantUrl) {
                        const data = await loadVariantImage(productId, targetAttributeId);
                        if(data.success) {
                            imageUrl = data.image_url;
                            imageUrlLarge = data.large_image_url;
                            // Pokud AJAX vrátil URL, použijeme ji. Jinak se spolehneme na stávající URL produktu.
                            // PrestaShop obvykle nemění URL produktu na stránce kategorie, jen obrázek.
                            // Ale pro jistotu, pokud by AJAX vracel URL, použijeme ji.
                            // V get_variant_image_basic.php se URL nevrací, takže to není problém.
                        }
                    }

                    if(imageUrl) {
                        updateProductDisplay(productElement, {
                            url: variantUrl || productElement.querySelector('.thumbnail.product-thumbnail')?.href, // Fallback na stávající URL
                            image: imageUrl,
                            imageLarge: imageUrlLarge
                        });
                    }
                }
                productElement.classList.add('variant-logic-initialized');
            }
        }

        updateProductImages();
    }
    
    // Prvotní spuštění
    initProductVariantLogic();

    // Posluchač pro AJAX aktualizace (faceted search)
    if (typeof prestashop !== 'undefined') {
        prestashop.on('updateProductList', function() {
            // Zpoždění je důležité, aby se DOM stihl aktualizovat po AJAXu faceted search
            setTimeout(initProductVariantLogic, 200); 
        });
    }
});
</script>
{/literal}

<style>
.product-image {
    transition: opacity 0.4s ease-in-out;
}
.product-image.loading {
    opacity: 0.5;
}
</style>
{/block}